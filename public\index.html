<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>weiwei智能聊天助手</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>weiwei智能聊天助手</h1>
            <p>基于本地部署的AI模型，支持局域网访问</p>
        </header>
        <div class="main-content">
            <div class="sidebar">
                <div class="model-selector">
                    <h3>选择模型</h3>
                    <select id="modelSelect">
                        <option value="">加载中...</option>
                    </select>
                    <button id="refreshModels" class="btn-secondary">刷新模型列表</button>
                </div>
                <div class="settings">
                    <h3>设置</h3>
                    <label>
                        <input type="checkbox" id="streamMode" checked>
                        流式输出（实时显示）
                    </label>
                </div>
                <div class="status">
                    <h3>服务状态</h3>
                    <div class="status-indicator">
                        <span class="status-dot"></span>
                        <span id="statusText">检查中...</span>
                    </div>
                </div>
            </div>
            <div class="chat-area">
                <div id="chatMessages" class="chat-messages">
                    <div class="message system">
                        <div class="message-content">
                            欢迎使用 Ollama AI 聊天助手！请选择模型并开始对话。
                        </div>
                    </div>
                </div>
                <div class="input-container">
                    <textarea 
                        id="messageInput" 
                        placeholder="输入你的问题..."
                        rows="3"
                    ></textarea>
                </div>
                <div class="input-actions">
                    <button id="sendButton" class="btn-primary" disabled>发送</button>
                    <button id="clearChat" class="btn-secondary">清空聊天</button>
                    <button id="stopGeneration" class="btn-secondary">停止生成</button>
                </div>
            </div>
        </div>
    </div>
    <div id="imgGenModal" class="modal" style="display:none;">
        <div class="modal-content">
            <span class="close" id="closeImgGen">&times;</span>
            <h2>AI图片生成 <span style="font-size:0.7em;color:#888;">by weiwei</span></h2>
            <div style="margin-bottom:12px;">
                <input id="imgGenPrompt" type="text" placeholder="请输入图片描述，如：一只可爱的猫" style="width:80%;padding:8px;">
                <button id="imgGenSubmit" class="btn-primary">生成图片</button>
            </div>
            <div id="imgGenResult" style="min-height:200px;"></div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html> 